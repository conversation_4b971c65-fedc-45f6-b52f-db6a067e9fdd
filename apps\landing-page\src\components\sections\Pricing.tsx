import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { useState, useEffect } from 'react';
import { CheckIcon, StarIcon } from '@heroicons/react/24/solid';

export default function Pricing() {
  const { t } = useTranslation('landing');
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Track mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Get pricing plans from translation
  const plans = Array.from({ length: 3 }, (_, i) => ({
    name: t(`pricing.plans.${i}.name`),
    price: t(`pricing.plans.${i}.price`),
    currency: t(`pricing.plans.${i}.currency`),
    period: t(`pricing.plans.${i}.period`),
    description: t(`pricing.plans.${i}.description`),
    features: Array.from({ length: 5 }, (_, j) => {
      const feature = t(`pricing.plans.${i}.features.${j}`, { defaultValue: null });
      return feature !== null ? feature : null;
    }).filter(Boolean),
    cta: t(`pricing.plans.${i}.cta`),
    popular: t(`pricing.plans.${i}.popular`) === 'true',
  }));

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section
      id="pricing"
      ref={ref}
      className="relative section-padding overflow-hidden"
      style={{
        background: `
          radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(245, 158, 11, 0.08) 0%, transparent 60%),
          linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)
        `
      }}
    >
      {/* Enhanced Background with Glass Orbs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Glass Orbs */}
        <motion.div
          animate={{
            y: [-20, 20, -20],
            rotate: [0, 180, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 16, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-20 right-10 w-28 h-28 rounded-full opacity-12"
          style={{
            background: 'rgba(245, 158, 11, 0.1)',
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
            border: '1px solid rgba(245, 158, 11, 0.2)',
            boxShadow: '0 8px 32px rgba(245, 158, 11, 0.1)'
          }}
        />

        <motion.div
          animate={{
            y: [30, -30, 30],
            rotate: [360, 180, 0],
            scale: [1.1, 1, 1.1]
          }}
          transition={{ duration: 20, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute bottom-28 left-8 w-36 h-36 rounded-full opacity-10"
          style={{
            background: 'rgba(168, 85, 247, 0.1)',
            backdropFilter: 'blur(25px)',
            WebkitBackdropFilter: 'blur(25px)',
            border: '1px solid rgba(168, 85, 247, 0.2)',
            boxShadow: '0 8px 32px rgba(168, 85, 247, 0.1)'
          }}
        />

        {/* Floating Particles */}
        {[...Array(5)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [-10, 10, -10],
              x: [-5, 5, -5],
              opacity: [0.1, 0.3, 0.1],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 4 + i * 1,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 1
            }}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              left: `${25 + (i * 15)}%`,
              top: `${35 + (i * 12)}%`,
              filter: 'blur(0.5px)'
            }}
          />
        ))}
      </div>
      <div className="container mx-auto container-padding">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="heading-lg mb-4 text-gray-900 dark:text-white"
          >
            {t('pricing.title')}
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-4"
          >
            {t('pricing.subtitle')}
          </motion.p>
          <motion.p
            variants={itemVariants}
            className="text-sm text-gray-500 dark:text-gray-400"
          >
            {t('pricing.note')}
          </motion.p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto"
        >
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className={`relative card p-8 ${
                plan.popular
                  ? 'ring-2 ring-primary-500 shadow-2xl scale-105'
                  : 'hover:shadow-xl'
              } transition-all duration-300`}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center gap-1">
                    <StarIcon className="w-4 h-4" />
                    {isRTL ? 'الأكثر شعبية' : 'Most Popular'}
                  </div>
                </div>
              )}

              {/* Plan Header */}
              <div className="text-center mb-8">
                <h3 className="heading-sm mb-2 text-gray-900 dark:text-white">
                  {plan.name}
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  {plan.description}
                </p>
                
                {/* Price */}
                <div className="flex items-baseline justify-center gap-1">
                  <span className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white">
                    {plan.price === '0' ? (isRTL ? 'مجاني' : 'Free') : plan.price}
                  </span>
                  {plan.price !== '0' && (
                    <>
                      <span className="text-lg text-gray-500 dark:text-gray-400">
                        {plan.currency}
                      </span>
                      <span className="text-gray-500 dark:text-gray-400">
                        / {plan.period}
                      </span>
                    </>
                  )}
                </div>
              </div>

              {/* Features */}
              <div className="mb-8">
                <ul className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-3">
                      <CheckIcon className="w-5 h-5 text-success-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-600 dark:text-gray-300">
                        {feature}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* CTA Button */}
              <button
                className={`w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 ${
                  plan.popular
                    ? 'bg-primary-600 hover:bg-primary-700 text-white shadow-lg hover:shadow-xl'
                    : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white'
                }`}
              >
                {plan.cta}
              </button>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional Info */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="text-center mt-16"
        >
          <div className="bg-white dark:bg-gray-700 rounded-2xl p-8 shadow-lg max-w-4xl mx-auto">
            <h3 className="heading-sm mb-4 text-gray-900 dark:text-white">
              {isRTL ? 'لديك أسئلة حول الأسعار؟' : 'Questions about pricing?'}
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              {isRTL 
                ? 'فريقنا جاهز لمساعدتك في اختيار الخطة المناسبة لاحتياجاتك'
                : 'Our team is ready to help you choose the right plan for your needs'
              }
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <button className="btn-primary">
                {isRTL ? 'تواصل معنا' : 'Contact Us'}
              </button>
              <button className="btn-outline">
                {isRTL ? 'جدولة مكالمة' : 'Schedule a Call'}
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
