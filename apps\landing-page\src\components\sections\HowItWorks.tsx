import { useState, useEffect } from 'react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import {
  DocumentPlusIcon,
  InboxArrowDownIcon,
  UserIcon,
  PlayIcon,
  UserPlusIcon,
  MagnifyingGlassIcon,
  PaperAirplaneIcon,
  BanknotesIcon,
} from '@heroicons/react/24/outline';

export default function HowItWorks() {
  const { t } = useTranslation('landing');
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const [activeTab, setActiveTab] = useState<'clients' | 'experts'>('clients');

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Track mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Steps for clients
  const clientSteps = [
    {
      title: t('howItWorks.forClients.steps.0.title'),
      description: t('howItWorks.forClients.steps.0.description'),
      icon: DocumentPlusIcon,
    },
    {
      title: t('howItWorks.forClients.steps.1.title'),
      description: t('howItWorks.forClients.steps.1.description'),
      icon: InboxArrowDownIcon,
    },
    {
      title: t('howItWorks.forClients.steps.2.title'),
      description: t('howItWorks.forClients.steps.2.description'),
      icon: UserIcon,
    },
    {
      title: t('howItWorks.forClients.steps.3.title'),
      description: t('howItWorks.forClients.steps.3.description'),
      icon: PlayIcon,
    },
  ];

  // Steps for experts
  const expertSteps = [
    {
      title: t('howItWorks.forExperts.steps.0.title'),
      description: t('howItWorks.forExperts.steps.0.description'),
      icon: UserPlusIcon,
    },
    {
      title: t('howItWorks.forExperts.steps.1.title'),
      description: t('howItWorks.forExperts.steps.1.description'),
      icon: MagnifyingGlassIcon,
    },
    {
      title: t('howItWorks.forExperts.steps.2.title'),
      description: t('howItWorks.forExperts.steps.2.description'),
      icon: PaperAirplaneIcon,
    },
    {
      title: t('howItWorks.forExperts.steps.3.title'),
      description: t('howItWorks.forExperts.steps.3.description'),
      icon: BanknotesIcon,
    },
  ];

  const currentSteps = activeTab === 'clients' ? clientSteps : expertSteps;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section
      id="how-it-works"
      ref={ref}
      className="relative section-padding overflow-hidden"
      style={{
        background: `
          radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(2, 132, 199, 0.08) 0%, transparent 60%),
          linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)
        `
      }}
    >
      {/* Enhanced Background with Glass Orbs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Glass Orbs */}
        <motion.div
          animate={{
            y: [-22, 22, -22],
            rotate: [0, 180, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 19, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-24 right-16 w-30 h-30 rounded-full opacity-12"
          style={{
            background: 'rgba(2, 132, 199, 0.1)',
            backdropFilter: 'blur(22px)',
            WebkitBackdropFilter: 'blur(22px)',
            border: '1px solid rgba(2, 132, 199, 0.2)',
            boxShadow: '0 8px 32px rgba(2, 132, 199, 0.1)'
          }}
        />

        <motion.div
          animate={{
            y: [28, -28, 28],
            rotate: [360, 180, 0],
            scale: [1.1, 1, 1.1]
          }}
          transition={{ duration: 21, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute bottom-36 left-14 w-26 h-26 rounded-full opacity-10"
          style={{
            background: 'rgba(124, 58, 237, 0.1)',
            backdropFilter: 'blur(18px)',
            WebkitBackdropFilter: 'blur(18px)',
            border: '1px solid rgba(124, 58, 237, 0.2)',
            boxShadow: '0 8px 32px rgba(124, 58, 237, 0.1)'
          }}
        />

        {/* Floating Particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [-12, 12, -12],
              x: [-6, 6, -6],
              opacity: [0.1, 0.3, 0.1],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 5 + i * 1.2,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 0.8
            }}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              left: `${20 + (i * 12)}%`,
              top: `${30 + (i * 10)}%`,
              filter: 'blur(0.5px)'
            }}
          />
        ))}
      </div>
      <div className="container mx-auto container-padding">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="heading-lg mb-4 text-gray-900 dark:text-white"
          >
            {t('howItWorks.title')}
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto"
          >
            {t('howItWorks.subtitle')}
          </motion.p>
        </motion.div>

        {/* Tab Navigation */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="flex justify-center mb-12"
        >
          <div className="bg-white dark:bg-gray-700 rounded-xl p-2 shadow-lg">
            <button
              onClick={() => setActiveTab('clients')}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                activeTab === 'clients'
                  ? 'bg-primary-600 text-white shadow-md'
                  : 'text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400'
              }`}
            >
              {t('howItWorks.forClients.title')}
            </button>
            <button
              onClick={() => setActiveTab('experts')}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                activeTab === 'experts'
                  ? 'bg-primary-600 text-white shadow-md'
                  : 'text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400'
              }`}
            >
              {t('howItWorks.forExperts.title')}
            </button>
          </div>
        </motion.div>

        {/* Steps */}
        <motion.div
          key={activeTab}
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {currentSteps.map((step, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="relative"
            >
              {/* Connection Line */}
              {index < currentSteps.length - 1 && (
                <div className="hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-primary-300 to-secondary-300 dark:from-primary-600 dark:to-secondary-600 z-0" />
              )}
              
              <div className="card p-6 text-center relative z-10 h-full">
                {/* Step Number */}
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
                  {index + 1}
                </div>
                
                {/* Icon */}
                <div className="w-12 h-12 mx-auto mb-4 bg-primary-100 dark:bg-primary-900 rounded-xl flex items-center justify-center">
                  <step.icon className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                </div>
                
                {/* Content */}
                <h3 className="heading-sm mb-3 text-gray-900 dark:text-white">
                  {step.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                  {step.description}
                </p>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="text-center mt-16"
        >
          <button className="btn-primary text-lg px-8 py-4">
            {activeTab === 'clients' 
              ? (isRTL ? 'انشر مشروعك الآن' : 'Post Your Project Now')
              : (isRTL ? 'انضم كخبير' : 'Join as Expert')
            }
          </button>
        </motion.div>
      </div>
    </section>
  );
}
