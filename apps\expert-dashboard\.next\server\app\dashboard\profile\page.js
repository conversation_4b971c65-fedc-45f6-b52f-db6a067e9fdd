(()=>{var e={};e.id=544,e.ids=[544],e.modules={5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},7091:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>g,tree:()=>o});var a=r(7096),s=r(6132),l=r(7284),i=r.n(l),d=r(2564),n={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);r.d(t,n);let o=["",{children:["dashboard",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,199)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,8182)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,2594)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,6097)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,5666)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\not-found.tsx"]}],c=["C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\profile\\page.tsx"],m="/dashboard/profile/page",x={require:r,loadChunk:()=>Promise.resolve()},g=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/dashboard/profile/page",pathname:"/dashboard/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},6280:(e,t,r)=>{Promise.resolve().then(r.bind(r,5493))},5493:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var a=r(3854),s=r(4218);let l=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.827 6.175A2.31 2.31 0 0 1 5.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 0 0-1.134-.175 2.31 2.31 0 0 1-1.64-1.055l-.822-1.316a2.192 2.192 0 0 0-1.736-1.039 48.774 48.774 0 0 0-5.232 0 2.192 2.192 0 0 0-1.736 1.039l-.821 1.316Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 12.75a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM18.75 10.5h.008v.008h-.008V10.5Z"}))}),i=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"}))});var d=r(8647);let n=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))});var o=r(9174),c=r(4700),m=r(4208);let x=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"}))}),g={id:"1",name:"أحمد محمد",title:"مطور ويب متخصص في React و Node.js",bio:"مطور ويب محترف مع أكثر من 5 سنوات من الخبرة في تطوير تطبيقات الويب الحديثة باستخدام React، Node.js، وقواعد البيانات المختلفة. أتخصص في بناء حلول تقنية مبتكرة وسهلة الاستخدام.",email:"<EMAIL>",phone:"+963 123 456 789",location:"دمشق، سوريا",languages:["العربية","الإنجليزية"],skills:["React","Node.js","TypeScript","PostgreSQL","MongoDB","AWS"],experience:5,education:"بكالوريوس هندسة معلوماتية - جامعة دمشق",certifications:["AWS Certified Developer","React Professional Certificate"],hourlyRate:25,availability:"متاح للعمل",verified:!0,rating:4.8,completedProjects:47,joinDate:"2023-01-15"};function h(){let[e,t]=(0,s.useState)(g),[r,h]=(0,s.useState)(!1),[p,u]=(0,s.useState)(g);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"sm:flex sm:items-center sm:justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"الملف الشخصي"}),a.jsx("p",{className:"mt-2 text-sm text-gray-700 dark:text-gray-300",children:"إدارة معلوماتك الشخصية ومهاراتك وخبراتك"})]}),a.jsx("div",{className:"mt-4 sm:mt-0",children:r?(0,a.jsxs)("div",{className:"flex space-x-3 rtl:space-x-reverse",children:[a.jsx("button",{onClick:()=>{u(e),h(!1)},className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600",children:"إلغاء"}),a.jsx("button",{onClick:()=>{t(p),h(!1)},className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700",children:"حفظ التغييرات"})]}):a.jsx("button",{onClick:()=>h(!0),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700",children:"تعديل الملف الشخصي"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[a.jsx("div",{className:"lg:col-span-1",children:a.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"relative inline-block",children:[a.jsx("div",{className:"h-32 w-32 rounded-full bg-primary-500 flex items-center justify-center mx-auto",children:a.jsx("span",{className:"text-4xl font-medium text-white",children:e.name.charAt(0)})}),r&&a.jsx("button",{className:"absolute bottom-0 right-0 bg-white dark:bg-gray-700 rounded-full p-2 shadow-lg border border-gray-200 dark:border-gray-600",children:a.jsx(l,{className:"h-4 w-4 text-gray-600 dark:text-gray-400"})})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white flex items-center justify-center",children:[e.name,e.verified&&a.jsx(i,{className:"h-5 w-5 text-blue-500 mr-2"})]}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:e.title})]}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[a.jsx(d.Z,{className:"h-4 w-4 text-yellow-400"}),a.jsx("span",{className:"mr-1 text-lg font-semibold text-gray-900 dark:text-white",children:e.rating})]}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"التقييم"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e.completedProjects}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"مشروع مكتمل"})]})]}),(0,a.jsxs)("div",{className:"mt-6 space-y-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center text-gray-600 dark:text-gray-400",children:[a.jsx(n,{className:"h-4 w-4 ml-2"}),e.location]}),(0,a.jsxs)("div",{className:"flex items-center text-gray-600 dark:text-gray-400",children:[a.jsx(o.Z,{className:"h-4 w-4 ml-2"}),"انضم في ",e.joinDate]}),(0,a.jsxs)("div",{className:"flex items-center text-gray-600 dark:text-gray-400",children:[a.jsx(c.Z,{className:"h-4 w-4 ml-2"}),e.experience," سنوات خبرة"]})]})]})})}),(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center",children:[a.jsx(m.Z,{className:"h-5 w-5 ml-2"}),"المعلومات الأساسية"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"الاسم الكامل"}),r?a.jsx("input",{type:"text",value:p.name,onChange:e=>u({...p,name:e.target.value}),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}):a.jsx("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:e.name})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"البريد الإلكتروني"}),r?a.jsx("input",{type:"email",value:p.email,onChange:e=>u({...p,email:e.target.value}),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}):a.jsx("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:e.email})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"رقم الهاتف"}),r?a.jsx("input",{type:"tel",value:p.phone,onChange:e=>u({...p,phone:e.target.value}),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}):a.jsx("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:e.phone})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"الموقع"}),r?a.jsx("input",{type:"text",value:p.location,onChange:e=>u({...p,location:e.target.value}),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}):a.jsx("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:e.location})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"العنوان المهني"}),r?a.jsx("input",{type:"text",value:p.title,onChange:e=>u({...p,title:e.target.value}),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}):a.jsx("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:e.title})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"نبذة عني"}),r?a.jsx("textarea",{rows:4,value:p.bio,onChange:e=>u({...p,bio:e.target.value}),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}):a.jsx("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:e.bio})]})]})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center",children:[a.jsx(x,{className:"h-5 w-5 ml-2"}),"المعلومات المهنية"]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"المهارات"}),a.jsx("div",{className:"mt-2 flex flex-wrap gap-2",children:e.skills.map((e,t)=>a.jsx("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300",children:e},t))})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"اللغات"}),a.jsx("div",{className:"mt-2 flex flex-wrap gap-2",children:e.languages.map((e,t)=>a.jsx("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",children:e},t))})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"سنوات الخبرة"}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:[e.experience," سنوات"]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"السعر بالساعة"}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:["$",e.hourlyRate,"/ساعة"]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"التعليم"}),a.jsx("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:e.education})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"الشهادات"}),a.jsx("ul",{className:"mt-2 space-y-1",children:e.certifications.map((e,t)=>(0,a.jsxs)("li",{className:"text-sm text-gray-900 dark:text-white",children:["• ",e]},t))})]})]})]})]})]})]})}},199:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var a=r(5153);let s=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\src\app\dashboard\profile\page.tsx`),{__esModule:l,$$typeof:i}=s,d=s.default,n=d},8647:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var a=r(4218);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))}),l=s}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[262,618,629],()=>r(7091));module.exports=a})();