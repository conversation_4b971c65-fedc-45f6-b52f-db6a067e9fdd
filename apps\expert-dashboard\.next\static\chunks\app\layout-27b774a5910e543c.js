(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{3688:function(e,n,r){Promise.resolve().then(r.t.bind(r,5537,23)),Promise.resolve().then(r.t.bind(r,2489,23)),Promise.resolve().then(r.bind(r,9850))},9850:function(e,n,r){"use strict";r.r(n),r.d(n,{Providers:function(){return f}});var t=r(7437),s=r(3115),i=r(8650),o=r(9332),a=r(8748),c=r(2265);function f(e){let{children:n}=e,[r]=(0,c.useState)(()=>new s.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return(0,t.jsx)(i.aH,{client:r,children:(0,t.jsxs)(o.f,{attribute:"class",defaultTheme:"dark",enableSystem:!1,disableTransitionOnChange:!0,children:[n,(0,t.jsx)(a.x7,{position:"top-center",toastOptions:{duration:4e3,style:{background:"#1f2937",color:"#f9fafb",border:"1px solid #374151"},success:{iconTheme:{primary:"#10b981",secondary:"#f9fafb"}},error:{iconTheme:{primary:"#ef4444",secondary:"#f9fafb"}}}})]})})}},2489:function(){}},function(e){e.O(0,[829,971,472,744],function(){return e(e.s=3688)}),_N_E=e.O()}]);