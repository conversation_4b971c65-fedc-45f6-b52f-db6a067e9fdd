"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/sections/HowItWorks.tsx":
/*!************************************************!*\
  !*** ./src/components/sections/HowItWorks.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HowItWorks; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-intersection-observer */ \"../../node_modules/react-intersection-observer/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BanknotesIcon,DocumentPlusIcon,InboxArrowDownIcon,MagnifyingGlassIcon,PaperAirplaneIcon,PlayIcon,UserIcon,UserPlusIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=BanknotesIcon,DocumentPlusIcon,InboxArrowDownIcon,MagnifyingGlassIcon,PaperAirplaneIcon,PlayIcon,UserIcon,UserPlusIcon!=!../../node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction HowItWorks() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"landing\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { locale } = router;\n    const isRTL = locale === \"ar\";\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"clients\");\n    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_4__.useInView)({\n        triggerOnce: true,\n        threshold: 0.1\n    });\n    // Track mouse movement for interactive effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX / window.innerWidth * 100,\n                y: e.clientY / window.innerHeight * 100\n            });\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>window.removeEventListener(\"mousemove\", handleMouseMove);\n    }, []);\n    // Steps for clients\n    const clientSteps = [\n        {\n            title: t(\"howItWorks.forClients.steps.0.title\"),\n            description: t(\"howItWorks.forClients.steps.0.description\"),\n            icon: _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.DocumentPlusIcon\n        },\n        {\n            title: t(\"howItWorks.forClients.steps.1.title\"),\n            description: t(\"howItWorks.forClients.steps.1.description\"),\n            icon: _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.InboxArrowDownIcon\n        },\n        {\n            title: t(\"howItWorks.forClients.steps.2.title\"),\n            description: t(\"howItWorks.forClients.steps.2.description\"),\n            icon: _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserIcon\n        },\n        {\n            title: t(\"howItWorks.forClients.steps.3.title\"),\n            description: t(\"howItWorks.forClients.steps.3.description\"),\n            icon: _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.PlayIcon\n        }\n    ];\n    // Steps for experts\n    const expertSteps = [\n        {\n            title: t(\"howItWorks.forExperts.steps.0.title\"),\n            description: t(\"howItWorks.forExperts.steps.0.description\"),\n            icon: _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserPlusIcon\n        },\n        {\n            title: t(\"howItWorks.forExperts.steps.1.title\"),\n            description: t(\"howItWorks.forExperts.steps.1.description\"),\n            icon: _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.MagnifyingGlassIcon\n        },\n        {\n            title: t(\"howItWorks.forExperts.steps.2.title\"),\n            description: t(\"howItWorks.forExperts.steps.2.description\"),\n            icon: _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.PaperAirplaneIcon\n        },\n        {\n            title: t(\"howItWorks.forExperts.steps.3.title\"),\n            description: t(\"howItWorks.forExperts.steps.3.description\"),\n            icon: _barrel_optimize_names_BanknotesIcon_DocumentPlusIcon_InboxArrowDownIcon_MagnifyingGlassIcon_PaperAirplaneIcon_PlayIcon_UserIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.BanknotesIcon\n        }\n    ];\n    const currentSteps = activeTab === \"clients\" ? clientSteps : expertSteps;\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"how-it-works\",\n        ref: ref,\n        className: \"relative section-padding overflow-hidden\",\n        style: {\n            background: \"\\n          radial-gradient(circle at \".concat(mousePosition.x, \"% \").concat(mousePosition.y, \"%, rgba(2, 132, 199, 0.08) 0%, transparent 60%),\\n          linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)\\n        \")\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            y: [\n                                -22,\n                                22,\n                                -22\n                            ],\n                            rotate: [\n                                0,\n                                180,\n                                360\n                            ],\n                            scale: [\n                                1,\n                                1.1,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 19,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-24 right-16 w-30 h-30 rounded-full opacity-12\",\n                        style: {\n                            background: \"rgba(2, 132, 199, 0.1)\",\n                            backdropFilter: \"blur(22px)\",\n                            WebkitBackdropFilter: \"blur(22px)\",\n                            border: \"1px solid rgba(2, 132, 199, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(2, 132, 199, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            y: [\n                                28,\n                                -28,\n                                28\n                            ],\n                            rotate: [\n                                360,\n                                180,\n                                0\n                            ],\n                            scale: [\n                                1.1,\n                                1,\n                                1.1\n                            ]\n                        },\n                        transition: {\n                            duration: 21,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute bottom-36 left-14 w-26 h-26 rounded-full opacity-10\",\n                        style: {\n                            background: \"rgba(124, 58, 237, 0.1)\",\n                            backdropFilter: \"blur(18px)\",\n                            WebkitBackdropFilter: \"blur(18px)\",\n                            border: \"1px solid rgba(124, 58, 237, 0.2)\",\n                            boxShadow: \"0 8px 32px rgba(124, 58, 237, 0.1)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    [\n                        ...Array(6)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            animate: {\n                                y: [\n                                    -12,\n                                    12,\n                                    -12\n                                ],\n                                x: [\n                                    -6,\n                                    6,\n                                    -6\n                                ],\n                                opacity: [\n                                    0.1,\n                                    0.3,\n                                    0.1\n                                ],\n                                scale: [\n                                    1,\n                                    1.2,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 5 + i * 1.2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: i * 0.8\n                            },\n                            className: \"absolute w-1 h-1 bg-white rounded-full\",\n                            style: {\n                                left: \"\".concat(20 + i * 12, \"%\"),\n                                top: \"\".concat(30 + i * 10, \"%\"),\n                                filter: \"blur(0.5px)\"\n                            }\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto container-padding\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: inView ? \"visible\" : \"hidden\",\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.h2, {\n                                variants: itemVariants,\n                                className: \"heading-lg mb-4 text-gray-900 dark:text-white\",\n                                children: t(\"howItWorks.title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                variants: itemVariants,\n                                className: \"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\",\n                                children: t(\"howItWorks.subtitle\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        variants: itemVariants,\n                        initial: \"hidden\",\n                        animate: inView ? \"visible\" : \"hidden\",\n                        className: \"flex justify-center mb-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-700 rounded-xl p-2 shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"clients\"),\n                                    className: \"px-6 py-3 rounded-lg font-medium transition-all duration-200 \".concat(activeTab === \"clients\" ? \"bg-primary-600 text-white shadow-md\" : \"text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400\"),\n                                    children: t(\"howItWorks.forClients.title\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"experts\"),\n                                    className: \"px-6 py-3 rounded-lg font-medium transition-all duration-200 \".concat(activeTab === \"experts\" ? \"bg-primary-600 text-white shadow-md\" : \"text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400\"),\n                                    children: t(\"howItWorks.forExperts.title\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: \"visible\",\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                        children: currentSteps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                variants: itemVariants,\n                                className: \"relative\",\n                                children: [\n                                    index < currentSteps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-primary-300 to-secondary-300 dark:from-primary-600 dark:to-secondary-600 z-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card p-6 text-center relative z-10 h-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center text-white font-bold text-xl\",\n                                                children: index + 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 mx-auto mb-4 bg-primary-100 dark:bg-primary-900 rounded-xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(step.icon, {\n                                                    className: \"w-6 h-6 text-primary-600 dark:text-primary-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"heading-sm mb-3 text-gray-900 dark:text-white\",\n                                                children: step.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-300 text-sm leading-relaxed\",\n                                                children: step.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this))\n                    }, activeTab, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        variants: itemVariants,\n                        initial: \"hidden\",\n                        animate: inView ? \"visible\" : \"hidden\",\n                        className: \"text-center mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"btn-primary text-lg px-8 py-4\",\n                            children: activeTab === \"clients\" ? isRTL ? \"انشر مشروعك الآن\" : \"Post Your Project Now\" : isRTL ? \"انضم كخبير\" : \"Join as Expert\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\components\\\\sections\\\\HowItWorks.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n_s(HowItWorks, \"TDOWiIeeiyRYv+PwmIzLMv4EnIg=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        react_intersection_observer__WEBPACK_IMPORTED_MODULE_4__.useInView\n    ];\n});\n_c = HowItWorks;\nvar _c;\n$RefreshReg$(_c, \"HowItWorks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/sections/HowItWorks.tsx\n"));

/***/ })

});